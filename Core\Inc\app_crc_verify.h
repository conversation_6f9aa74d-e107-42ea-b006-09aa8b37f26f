/**
 * @file app_crc_verify.h
 * @brief 应用程序CRC验证功能
 * @date 2025-08-22
 * <AUTHOR>
 */

#ifndef __APP_CRC_VERIFY_H__
#define __APP_CRC_VERIFY_H__

#include <stdint.h>
#include <stdbool.h>
#include "stm32f1xx_hal.h"

/* 应用程序基地址 (与IAP模块保持一致) */
#define APP_BASE_ADDRESS    0x08005000  // 兼容性定义，指向APP1

/* CRC32计算参数 */
#define CRC32_POLYNOMIAL    0x04C11DB7
#define CRC32_INIT_VALUE    0x00000000

/* 应用程序最大大小 (根据Flash布局设定) */
#define MAX_APP_SIZE        0x1B000  // 108KB (原始单分区大小)
#define MAX_PARTITION_SIZE  0x0D000  // 52KB (双分区模式下单个分区大小)

/* 魔术字节定义 */
#define FIRMWARE_MAGIC      0x12345678

/* 固件尾部信息结构 (新方案) */
typedef struct {
    uint32_t version;       // 固件版本号
    uint32_t firmware_size; // 固件实际大小（不包含这16字节）
    uint32_t crc32;         // CRC32校验值
    uint32_t magic;         // 魔术字节 0x12345678
} firmware_tail_t;

/* 固件尾部信息大小 */
#define FIRMWARE_TAIL_SIZE  sizeof(firmware_tail_t)  // 16字节

/**
 * @brief 计算数据的CRC32值
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC32值
 */
uint32_t calculate_crc32(const uint8_t *data, uint32_t length);

/**
 * @brief 验证应用程序的CRC
 * @param app_start_addr 应用程序起始地址
 * @param app_size 应用程序总大小 (包括CRC的4字节)
 * @return true: 验证通过, false: 验证失败
 */
bool verify_app_crc(uint32_t app_start_addr, uint32_t app_size);

/**
 * @brief 通过搜索魔术字节找到固件尾部并获取大小
 * @param start_addr 固件起始地址
 * @param max_size 最大搜索范围
 * @return 固件总大小（包含16字节尾部），0表示未找到
 */
uint32_t find_firmware_tail_by_magic(uint32_t start_addr, uint32_t max_size);

/**
 * @brief 检查应用程序是否有效（包括向量表和CRC验证）
 * @return true: 应用程序有效, false: 应用程序无效
 */
bool is_app_valid(void);

/**
 * @brief 验证固件更新完成后的完整性
 * @param written_bytes 实际写入的字节数
 * @return true: 验证通过, false: 验证失败
 */
bool verify_firmware_after_update(uint32_t written_bytes);

/**
 * @brief 解析固件尾部信息
 * @param start_addr 固件起始地址
 * @param total_size 固件总大小（包含16字节尾部）
 * @param tail_info 输出的尾部信息结构
 * @return true: 解析成功, false: 解析失败
 */
bool parse_firmware_tail(uint32_t start_addr, uint32_t total_size, firmware_tail_t *tail_info);

/**
 * @brief 验证魔术字节
 * @param magic 待验证的魔术字节
 * @return true: 验证通过, false: 验证失败
 */
bool verify_magic_bytes(uint32_t magic);

#endif /* __APP_CRC_VERIFY_H__ */
