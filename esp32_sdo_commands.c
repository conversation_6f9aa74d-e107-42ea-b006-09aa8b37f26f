// ESP32上位机SDO命令发送示例
// 适用于触摸屏按键控制

#include "driver/twai.h"
#include "esp_log.h"

#define TAG "SDO_CMD"
#define NODE_ID 0x01
#define SDO_RX_COB_ID (0x600 + NODE_ID)  // 发送给Bootloader的SDO COB-ID

// 对象字典索引
#define OD_BOOT_COMMAND 0x2001
#define OD_BOOT_STATUS  0x2002

// 命令码
#define CMD_ERASE_APP1   0x01
#define CMD_ERASE_APP2   0x02
#define CMD_JUMP_APP1    0x11
#define CMD_JUMP_APP2    0x12
#define CMD_SOFT_RESET   0xFF

// 发送SDO写命令
static void send_sdo_write_u8(uint16_t index, uint8_t subindex, uint8_t value)
{
    twai_message_t message;
    message.identifier = SDO_RX_COB_ID;
    message.flags = TWAI_MSG_FLAG_NONE;
    message.data_length_code = 8;
    
    message.data[0] = 0x2F;  // SDO下载，1字节数据
    message.data[1] = index & 0xFF;
    message.data[2] = (index >> 8) & 0xFF;
    message.data[3] = subindex;
    message.data[4] = value;
    message.data[5] = 0x00;
    message.data[6] = 0x00;
    message.data[7] = 0x00;
    
    if (twai_transmit(&message, pdMS_TO_TICKS(1000)) == ESP_OK) {
        ESP_LOGI(TAG, "SDO write sent: Index=0x%04X, Value=0x%02X", index, value);
    } else {
        ESP_LOGE(TAG, "Failed to send SDO write");
    }
}

// 发送SDO读命令
static void send_sdo_read(uint16_t index, uint8_t subindex)
{
    twai_message_t message;
    message.identifier = SDO_RX_COB_ID;
    message.flags = TWAI_MSG_FLAG_NONE;
    message.data_length_code = 8;
    
    message.data[0] = 0x40;  // SDO上传请求
    message.data[1] = index & 0xFF;
    message.data[2] = (index >> 8) & 0xFF;
    message.data[3] = subindex;
    message.data[4] = 0x00;
    message.data[5] = 0x00;
    message.data[6] = 0x00;
    message.data[7] = 0x00;
    
    if (twai_transmit(&message, pdMS_TO_TICKS(1000)) == ESP_OK) {
        ESP_LOGI(TAG, "SDO read sent: Index=0x%04X", index);
    } else {
        ESP_LOGE(TAG, "Failed to send SDO read");
    }
}

// 触摸屏按键处理函数
void handle_bootloader_buttons(int button_id)
{
    switch(button_id) {
        case 1: // 擦除APP1按键
            ESP_LOGI(TAG, "Button: Erase APP1");
            send_sdo_write_u8(OD_BOOT_COMMAND, 0, CMD_ERASE_APP1);
            break;
            
        case 2: // 擦除APP2按键
            ESP_LOGI(TAG, "Button: Erase APP2");
            send_sdo_write_u8(OD_BOOT_COMMAND, 0, CMD_ERASE_APP2);
            break;
            
        case 3: // 启动APP1按键
            ESP_LOGI(TAG, "Button: Jump to APP1");
            send_sdo_write_u8(OD_BOOT_COMMAND, 0, CMD_JUMP_APP1);
            break;
            
        case 4: // 启动APP2按键
            ESP_LOGI(TAG, "Button: Jump to APP2");
            send_sdo_write_u8(OD_BOOT_COMMAND, 0, CMD_JUMP_APP2);
            break;
            
        case 5: // 软复位按键
            ESP_LOGI(TAG, "Button: Soft Reset");
            send_sdo_write_u8(OD_BOOT_COMMAND, 0, CMD_SOFT_RESET);
            break;
            
        case 6: // 状态查询按键
            ESP_LOGI(TAG, "Button: Query Status");
            send_sdo_read(OD_BOOT_STATUS, 0);
            break;
            
        default:
            ESP_LOGW(TAG, "Unknown button: %d", button_id);
            break;
    }
}

// SDO响应处理函数
void handle_sdo_response(twai_message_t *message)
{
    if (message->identifier == (0x580 + NODE_ID)) {
        uint8_t cmd = message->data[0];
        uint16_t index = message->data[1] | (message->data[2] << 8);
        uint8_t subindex = message->data[3];
        
        if (cmd == 0x60) {
            // SDO写响应
            ESP_LOGI(TAG, "SDO Write OK: Index=0x%04X", index);
        } else if (cmd == 0x40) {
            // SDO读响应
            if (index == OD_BOOT_STATUS) {
                uint32_t status = message->data[4] | (message->data[5] << 8) | 
                                (message->data[6] << 16) | (message->data[7] << 24);
                ESP_LOGI(TAG, "Boot Status: 0x%08lX", status);
                
                // 根据状态显示信息
                switch(status) {
                    case 0x00000000:
                        ESP_LOGI(TAG, "Status: OK");
                        break;
                    case 0x00000001:
                        ESP_LOGE(TAG, "Status: Invalid Key");
                        break;
                    case 0x00000002:
                        ESP_LOGE(TAG, "Status: Invalid Command");
                        break;
                    case 0x00000003:
                        ESP_LOGE(TAG, "Status: Erase Failed");
                        break;
                    default:
                        ESP_LOGW(TAG, "Status: Unknown (0x%08lX)", status);
                        break;
                }
            }
        } else if (cmd == 0x80) {
            // SDO中止响应
            uint32_t abort_code = message->data[4] | (message->data[5] << 8) | 
                                (message->data[6] << 16) | (message->data[7] << 24);
            ESP_LOGE(TAG, "SDO Abort: Index=0x%04X, Code=0x%08lX", index, abort_code);
        }
    }
}
