#include "app_crc_verify.h"
#include "stmflash.h"
#include "usart.h"
#include "stdio.h"
#include "iap.h"  // 添加IAP头文件以使用标志位函数


/**
 * @brief 验证魔术字节
 * @param magic 待验证的魔术字节
 * @return true: 验证通过, false: 验证失败
 */
bool verify_magic_bytes(uint32_t magic)
{
    if (magic == FIRMWARE_MAGIC) {
        printf("Magic bytes verification passed: 0x%08lX\r\n", magic);
        return true;
    } else {
        printf("Magic bytes verification failed: 0x%08lX (expected: 0x%08lX)\r\n", magic, FIRMWARE_MAGIC);
        return false;
    }
}

/**
 * @brief 解析固件尾部信息
 * @param start_addr 固件起始地址
 * @param total_size 固件总大小（包含16字节尾部）
 * @param tail_info 输出的尾部信息结构
 * @return true: 解析成功, false: 解析失败
 */
bool parse_firmware_tail(uint32_t start_addr, uint32_t total_size, firmware_tail_t *tail_info)
{
    // 参数检查
    if (tail_info == NULL || total_size < FIRMWARE_TAIL_SIZE) {
        printf("Parse firmware tail failed: invalid parameters\r\n");
        return false;
    }
    
    // 计算尾部信息的位置
    uint8_t *tail_addr = (uint8_t *)(start_addr + total_size - FIRMWARE_TAIL_SIZE);
    
    // 读取尾部信息（小端格式）
    tail_info->version = (uint32_t)tail_addr[0] |
                        ((uint32_t)tail_addr[1] << 8) |
                        ((uint32_t)tail_addr[2] << 16) |
                        ((uint32_t)tail_addr[3] << 24);
    
    tail_info->firmware_size = (uint32_t)tail_addr[4] |
                              ((uint32_t)tail_addr[5] << 8) |
                              ((uint32_t)tail_addr[6] << 16) |
                              ((uint32_t)tail_addr[7] << 24);
    
    tail_info->crc32 = (uint32_t)tail_addr[8] |
                      ((uint32_t)tail_addr[9] << 8) |
                      ((uint32_t)tail_addr[10] << 16) |
                      ((uint32_t)tail_addr[11] << 24);
    
    tail_info->magic = (uint32_t)tail_addr[12] |
                      ((uint32_t)tail_addr[13] << 8) |
                      ((uint32_t)tail_addr[14] << 16) |
                      ((uint32_t)tail_addr[15] << 24);
    
    printf("Firmware tail info:\r\n");
    printf("  Version: 0x%08lX\r\n", tail_info->version);
    printf("  Size: %lu bytes\r\n", tail_info->firmware_size);
    printf("  CRC32: 0x%08lX\r\n", tail_info->crc32);
    printf("  Magic: 0x%08lX\r\n", tail_info->magic);
    
    return true;
}

/**
 * @brief 验证固件尾部信息的合法性
 * @param tail_info 尾部信息结构
 * @param total_size 固件总大小
 * @return true: 合法, false: 不合法
 */
static bool validate_firmware_tail_info(const firmware_tail_t *tail_info, uint32_t total_size)
{
    // 验证魔术字节
    if (!verify_magic_bytes(tail_info->magic)) {
        return false;
    }
    
    // 验证固件大小合理性
    if (tail_info->firmware_size == 0 || tail_info->firmware_size > MAX_APP_SIZE) {
        printf("Invalid firmware size: %lu bytes\r\n", tail_info->firmware_size);
        return false;
    }
    
    // 验证固件大小与总大小的一致性
    if (tail_info->firmware_size + FIRMWARE_TAIL_SIZE != total_size) {
        printf("Size mismatch: firmware_size(%lu) + tail_size(%d) != total_size(%lu)\r\n",
               tail_info->firmware_size, FIRMWARE_TAIL_SIZE, total_size);
        return false;
    }
    
    return true;
}

/**
 * @brief 计算数据的CRC32值
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC32值
 * 
 * @note 使用与Python脚本相同的参数:
 *       - 多项式: 0x04C11DB7
 *       - 初值: 0x00000000
 *       - 不反转输入
 *       - 不反转输出
 *       - 不异或输出
 */
uint32_t calculate_crc32(const uint8_t *data, uint32_t length)
{
    uint32_t crc = CRC32_INIT_VALUE;
    uint32_t i, j;
    
    for (i = 0; i < length; i++) {
        uint8_t byte = data[i];
        
        // 将字节移到高位
        crc ^= (uint32_t)byte << 24;
        
        // 处理每一位
        for (j = 0; j < 8; j++) {
            if (crc & 0x80000000) {
                crc = (crc << 1) ^ CRC32_POLYNOMIAL;
            } else {
                crc = crc << 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief 验证应用程序的CRC
 * @param app_start_addr 应用程序起始地址
 * @param app_total_size 应用程序总大小 (包含16字节尾部)
 * @return true: 验证通过, false: 验证失败
 */
bool verify_app_crc(uint32_t app_start_addr, uint32_t app_total_size)
{

    // 解析固件尾部信息
    firmware_tail_t tail_info;
    if (!parse_firmware_tail(app_start_addr, app_total_size, &tail_info)) {
        printf("CRC check failed: cannot parse firmware tail\r\n");
        return false;
    }
    
    // 验证尾部信息合法性
    if (!validate_firmware_tail_info(&tail_info, app_total_size)) {
        printf("CRC check failed: invalid firmware tail info\r\n");
        return false;
    }
    
    printf("Firmware data size: %lu bytes\r\n", tail_info.firmware_size);
    printf("Stored CRC: 0x%08lX\r\n", tail_info.crc32);
    
    // 计算固件数据的CRC (不包含尾部16字节)
    uint32_t calculated_crc = calculate_crc32((uint8_t *)app_start_addr, tail_info.firmware_size);
    
    printf("Calculated CRC: 0x%08lX\r\n", calculated_crc);
    
    // 比较CRC值
    if (tail_info.crc32 == calculated_crc) {
        printf("CRC check passed!\r\n");
        return true;
    } else {
        printf("CRC check failed! (Stored: 0x%08lX, Calculated: 0x%08lX)\r\n", 
               tail_info.crc32, calculated_crc);
        return false;
    }
}

/**
 * @brief 通过搜索魔术字节找到固件尾部并获取大小
 * @param start_addr 固件起始地址
 * @param max_size 最大搜索范围
 * @return 固件总大小（包含16字节尾部），0表示未找到
 */
uint32_t find_firmware_tail_by_magic(uint32_t start_addr, uint32_t max_size)
{
    uint8_t *ptr = (uint8_t *)start_addr;

    // printf("Searching for firmware tail by magic bytes from 0x%08lX, max size: %lu bytes\r\n", start_addr, max_size);

    // 检查起始位置是否有有效的向量表（栈顶指针）
    uint32_t stack_ptr = *(volatile uint32_t*)start_addr;
    if ((stack_ptr & 0x2FFE0000) != 0x20000000) {
        printf("Search failed: invalid vector table at start address\r\n");
        return 0;
    }

    printf("Valid vector table detected\r\n");

    // 从后往前搜索魔术字节，确保至少有16字节的完整尾部
    for (uint32_t i = max_size; i >= FIRMWARE_TAIL_SIZE; i -= 4) {
        // 读取4字节并检查是否为魔术字节（小端格式）
        uint32_t magic = (uint32_t)ptr[i - 4] |
                        ((uint32_t)ptr[i - 3] << 8) |
                        ((uint32_t)ptr[i - 2] << 16) |
                        ((uint32_t)ptr[i - 1] << 24);

        if (magic == FIRMWARE_MAGIC) {
            printf("Found magic bytes at offset %lu (0x%08lX)\r\n", i - 4, magic);

            // 找到魔术字节，解析完整的16字节尾部信息
            uint32_t tail_start_offset = i - FIRMWARE_TAIL_SIZE;
            firmware_tail_t tail_info;

            // 解析尾部信息
            tail_info.version = (uint32_t)ptr[tail_start_offset] |
                               ((uint32_t)ptr[tail_start_offset + 1] << 8) |
                               ((uint32_t)ptr[tail_start_offset + 2] << 16) |
                               ((uint32_t)ptr[tail_start_offset + 3] << 24);

            tail_info.firmware_size = (uint32_t)ptr[tail_start_offset + 4] |
                                     ((uint32_t)ptr[tail_start_offset + 5] << 8) |
                                     ((uint32_t)ptr[tail_start_offset + 6] << 16) |
                                     ((uint32_t)ptr[tail_start_offset + 7] << 24);

            tail_info.crc32 = (uint32_t)ptr[tail_start_offset + 8] |
                             ((uint32_t)ptr[tail_start_offset + 9] << 8) |
                             ((uint32_t)ptr[tail_start_offset + 10] << 16) |
                             ((uint32_t)ptr[tail_start_offset + 11] << 24);

            tail_info.magic = magic;

            // 验证尾部信息的合理性
            if (tail_info.firmware_size == 0 || tail_info.firmware_size > MAX_APP_SIZE) {
                printf("Invalid firmware size in tail: %lu bytes, continue searching...\r\n", tail_info.firmware_size);
                continue;
            }

            // 验证固件大小与找到位置的一致性
            uint32_t expected_total_size = tail_info.firmware_size + FIRMWARE_TAIL_SIZE;
            if (expected_total_size != i) {
                printf("Size mismatch: expected total %lu, found at %lu, continue searching...\r\n", expected_total_size, i);
                continue;
            }

            // printf("Valid firmware tail found:\r\n");
            // printf("  Version: 0x%08lX\r\n", tail_info.version);
            // printf("  Firmware size: %lu bytes\r\n", tail_info.firmware_size);
            // printf("  CRC32: 0x%08lX\r\n", tail_info.crc32);
            // printf("  Total size: %lu bytes (including tail)\r\n", expected_total_size);

            return expected_total_size;
        }
    }

    printf("No valid firmware tail found with magic bytes\r\n");
    return 0;
}

/**
 * @brief 验证固件更新完成后的完整性
 * @param written_bytes 实际写入的字节数
 * @return true: 验证通过, false: 验证失败
 */
bool verify_firmware_after_update(uint32_t written_bytes)
{
    printf("==========================================\r\n");
    printf("Firmware update completed, starting verification...\r\n");
    printf("Written bytes: %lu\r\n", written_bytes);

    // 1. 检查向量表（栈指针）
    uint32_t stack_ptr = *(volatile uint32_t*)APP_BASE_ADDRESS;
    if ((stack_ptr & 0x2FFE0000) != 0x20000000) {
        printf("Vector table check failed: invalid stack pointer 0x%08lX\r\n", stack_ptr);
        printf("==========================================\r\n");
        return false;
    }

    // 2. 检查复位向量
    uint32_t reset_vector = *(volatile uint32_t*)(APP_BASE_ADDRESS + 4);
    if ((reset_vector & 0xFF000000) != 0x08000000) {
        printf("Vector table check failed: invalid reset vector 0x%08lX\r\n", reset_vector);
        printf("==========================================\r\n");
        return false;
    }

    // 3. 基于写入字节数查找魔术字节和解析尾部信息
    uint32_t firmware_size = find_firmware_tail_by_magic(APP_BASE_ADDRESS, written_bytes);
    if (firmware_size == 0) {
        printf("Failed to find valid firmware tail in written data\r\n");
        printf("==========================================\r\n");
        return false;
    }

    // 4. 进行CRC验证
    bool result = verify_app_crc(APP_BASE_ADDRESS, firmware_size);

    if (result) {
        printf("Firmware verification passed!\r\n");

        // 清除标志位并重启到新固件（默认进入APP）
        printf("Restarting to new application...\r\n");
        clear_boot_flag();
        HAL_Delay(100);
        NVIC_SystemReset();
    } else {
        printf("Firmware verification failed! Please re-burn the firmware.\r\n");
    }

    printf("==========================================\r\n");
    return result;
}


