#include "can.h"
#include "canopen.h"
#include "iap.h"
#include "string.h"
#include "stdio.h"

// CANopen SDO相关定义
#define SDO_CMD_DOWNLOAD_INIT    0x20  // 下载初始化(写对象)
#define SDO_CMD_UPLOAD_INIT      0x40  // 上传初始化(读对象)
#define SDO_RSP_DOWNLOAD_INIT    0x60  // 下载初始化响应
#define SDO_RSP_UPLOAD_INIT      0x40  // 上传初始化响应
#define SDO_CMD_ABORT            0x80  // 中止传输

// Bootloader对象字典索引
#define OD_BOOT_SESSION_KEY      0x2000  // 会话密钥(u32, WO)
#define OD_BOOT_COMMAND          0x2001  // 启动命令(u8, WO)
#define OD_BOOT_STATUS           0x2002  // 启动状态(u32, RO)
#define OD_CURRENT_PARTITION     0x2003  // 当前分区(u8, RO)
#define OD_PARTITION_INFO        0x2004  // 分区信息(array, RO)

// Bootloader命令定义
#define BOOT_CMD_ERASE_APP1      0x01
#define BOOT_CMD_ERASE_APP2      0x02
#define BOOT_CMD_JUMP_APP1       0x11
#define BOOT_CMD_JUMP_APP2       0x12
#define BOOT_CMD_SOFT_RESET      0xFF

// 会话密钥和状态码
#define BOOT_SESSION_KEY         0xB007C0DE
#define STATUS_OK                0x00000000
#define STATUS_INVALID_KEY       0x00000001
#define STATUS_INVALID_CMD       0x00000002
#define STATUS_ERASE_FAILED      0x00000003
#define STATUS_JUMP_FAILED       0x00000004
#define STATUS_BUSY              0x00000005

// SDO状态变量
static uint32_t boot_session_key = 0;
static uint32_t boot_status = STATUS_OK;
static uint8_t session_unlocked = 0;

// CANopen SDO相关定义
#define SDO_CMD_DOWNLOAD_INIT    0x20  // 下载初始化
#define SDO_CMD_DOWNLOAD_SEG     0x00  // 下载分段
#define SDO_CMD_UPLOAD_INIT      0x40  // 上传初始化
#define SDO_CMD_UPLOAD_SEG       0x60  // 上传分段
#define SDO_CMD_ABORT            0x80  // 中止传输

// SDO响应命令
#define SDO_RSP_DOWNLOAD_INIT    0x60  // 下载初始化响应
#define SDO_RSP_DOWNLOAD_SEG     0x20  // 下载分段响应
#define SDO_RSP_UPLOAD_INIT      0x40  // 上传初始化响应
#define SDO_RSP_UPLOAD_SEG       0x00  // 上传分段响应

// 对象字典索引定义
// Bootloader模式对象
#define OD_BOOT_SESSION_KEY      0x2000  // 会话密钥
#define OD_BOOT_COMMAND          0x2001  // 启动命令
#define OD_BOOT_STATUS           0x2002  // 启动状态
#define OD_CURRENT_PARTITION     0x2003  // 当前分区
#define OD_PARTITION_INFO        0x2004  // 分区信息

// APP模式对象
#define OD_MOTOR_CONTROL_WORD    0x6040  // 电机控制字
#define OD_MOTOR_STATUS_WORD     0x6041  // 电机状态字
#define OD_TARGET_CURRENT        0x2200  // 目标电流
#define OD_ACTUAL_CURRENT        0x2201  // 实际电流
#define OD_TARGET_VELOCITY       0x2202  // 目标速度
#define OD_ACTUAL_VELOCITY       0x2203  // 实际速度
#define OD_APP_SESSION_KEY       0x2100  // APP会话密钥
#define OD_APP_COMMAND           0x2101  // APP命令
#define OD_APP_STATUS            0x2102  // APP状态

// 命令定义
#define BOOT_CMD_ERASE_APP1      0x01
#define BOOT_CMD_ERASE_APP2      0x02
#define BOOT_CMD_JUMP_APP1       0x11
#define BOOT_CMD_JUMP_APP2       0x12
#define BOOT_CMD_SOFT_RESET      0xFF

#define APP_CMD_SWITCH_PARTITION 0x21
#define APP_CMD_SOFT_RESET       0xFF

// 会话密钥
#define BOOT_SESSION_KEY         0xB007C0DE
#define APP_SESSION_KEY          0xA99C0DE5

// 状态码定义
#define STATUS_OK                0x00000000
#define STATUS_INVALID_KEY       0x00000001
#define STATUS_INVALID_CMD       0x00000002
#define STATUS_ERASE_FAILED      0x00000003
#define STATUS_JUMP_FAILED       0x00000004
#define STATUS_BUSY              0x00000005
CAN_TxHeaderTypeDef TxMessage1; // CAN发送消息结构体

// 2个3位标识符FIFO
#define CAN1FIFO CAN_RX_FIFO0 // CAN1接收FIFO
#define CAN2FIFO CAN_RX_FIFO1 // CAN2接收FIFO

CAN_TxHeaderTypeDef TxMeg; // CAN发送消息头定义
CAN_RxHeaderTypeDef RxMeg; // CAN接收消息头定义

uint8_t CAN_Rx_Data[8]; // CAN接收数据缓冲区
uint8_t CAN_Tx_Data[8]; // CAN发送数据缓冲区

// 流式更新相关变量
uint8_t CAN_RX_BUF_A[CAN_REC_LEN]; // 缓冲区A
uint8_t CAN_RX_BUF_B[CAN_REC_LEN]; // 缓冲区B
uint8_t *current_rx_buffer = CAN_RX_BUF_A;     // 当前接收缓冲区指针
uint8_t *current_flash_buffer = CAN_RX_BUF_B;  // 当前Flash写入缓冲区指针
uint32_t current_flash_addr = 0;               // 当前Flash写入地址
uint32_t partition_base_addr = 0;              // 当前分区起始地址
uint32_t total_received_bytes = 0;             // 总接收字节数
uint8_t flash_write_pending = 0;               // Flash写入挂起标志
uint32_t last_receive_time = 0;                // 最后接收时间戳
uint8_t jump_to_app_cmd = 0;                   // 跳转到APP命令标志
uint8_t erase_flash_cmd = 0;                   // 擦除Flash命令标志

// uint8_t TPDO1_Data[8];		// 发送过程数据对象1缓冲区
// uint8_t RPDO1_Data[8];		// 接收过程数据对象1缓冲区
uint16_t CAN_Baudrate = 7;	// CAN波特率设置，默认为5（500kbit/s）
uint8_t Error_register = 0; // 错误寄存器

uint32_t CAN1_RX_CNT=0;

// SDO相关定义和变量
#define SDO_RX_COB_ID            (0x600 + NODE_ID)  // SDO接收COB-ID
#define SDO_TX_COB_ID            (0x580 + NODE_ID)  // SDO发送COB-ID
#define NODE_ID                  0x01               // 节点ID，可根据需要修改

// SDO命令定义
#define SDO_CMD_DOWNLOAD         0x20  // 写对象
#define SDO_CMD_UPLOAD           0x40  // 读对象
#define SDO_RSP_DOWNLOAD         0x60  // 写响应
#define SDO_RSP_UPLOAD           0x40  // 读响应

// 对象字典索引
#define OD_BOOT_COMMAND          0x2001  // 启动命令(u8)
#define OD_BOOT_STATUS           0x2002  // 启动状态(u32)

// 命令码
#define CMD_ERASE_APP1           0x01
#define CMD_ERASE_APP2           0x02
#define CMD_JUMP_APP1            0x11
#define CMD_JUMP_APP2            0x12
#define CMD_SOFT_RESET           0xFF

// 状态变量
static uint32_t sdo_boot_status = 0;  // 0=OK, 其他=错误码

// SDO处理函数声明
static void process_sdo_download(uint16_t index, uint8_t subindex, uint8_t *data, uint8_t len);
static void process_sdo_upload(uint16_t index, uint8_t subindex, uint8_t *response_data, uint8_t *response_len);
static void send_sdo_response(uint32_t cob_id, uint8_t cmd, uint16_t index, uint8_t subindex, uint8_t *data, uint8_t len);
static void send_sdo_abort(uint32_t cob_id, uint16_t index, uint8_t subindex, uint32_t abort_code);
static void execute_boot_command(uint8_t command);

void CAN_User_Init(CAN_HandleTypeDef *hcan) // CAN用户初始化函数
{
	CAN_FilterTypeDef sFilterConfig; // CAN过滤器配置结构体
	HAL_StatusTypeDef HAL_Status;	 // HAL状态变量

	TxMeg.IDE = CAN_ID_STD;	  // 使用标准帧ID
	TxMeg.RTR = CAN_RTR_DATA; // 数据帧

	switch (CAN_Baudrate) // 根据波特率设置预分频值
	{
	case 0:
		hcan->Init.Prescaler = 100; // 20kbit/s
		break;
	case 1:
		hcan->Init.Prescaler = 40; // 50kbit/s
		break;
	case 2:
		hcan->Init.Prescaler = 20; // 100kbit/s
		break;
	case 3:
		hcan->Init.Prescaler = 16; // 125kbit/s
		break;
	case 4:
		hcan->Init.Prescaler = 8; // 250kbit/s
		break;
	case 5:
		hcan->Init.Prescaler = 4; // 500kbit/s
		break;
	case 7:
		hcan->Init.Prescaler = 2; // 1000kbit/s
		break;
	default:
		hcan->Init.Prescaler = 8; // 默认250kbit/s
		break;
	}

	if (HAL_CAN_Init(hcan) != HAL_OK) // 初始化CAN
	{
		Error_Handler(); // 如果初始化失败，调用错误处理函数
	}

	sFilterConfig.FilterBank = 0;					   // 使用过滤器0
	sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;  // 设置为ID掩码模式
	sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT; // 32位过滤器

	sFilterConfig.FilterIdHigh = 0X0000; // 过滤器ID高16位
	sFilterConfig.FilterIdLow = 0X0000;	 // 过滤器ID低16位

	sFilterConfig.FilterMaskIdHigh = 0X0000;		   // 过滤器掩码高16位（0表示接收所有）
	sFilterConfig.FilterMaskIdLow = 0X0000;			   // 过滤器掩码低16位
	sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0; // 过滤器关联到FIFO0

	sFilterConfig.FilterActivation = ENABLE; // 激活过滤器
	sFilterConfig.SlaveStartFilterBank = 0;	 // 从模式起始过滤器编号

	HAL_Status = HAL_CAN_ConfigFilter(hcan, &sFilterConfig); // 配置CAN过滤器
	HAL_Status = HAL_CAN_Start(hcan);						 // 启动CAN

	HAL_Status = HAL_CAN_ActivateNotification(hcan, CAN_IT_RX_FIFO0_MSG_PENDING); // 激活FIFO0接收中断
}

void HAL_CAN_RxFifo0MsgPendingCallback(CAN_HandleTypeDef *hcan) // CAN接收中断回调函数
{
	HAL_StatusTypeDef HAL_RetVal;

	HAL_RetVal = HAL_CAN_GetRxMessage(hcan, CAN1FIFO, &RxMeg, CAN_Rx_Data); // 获取接收到的消息
	if (HAL_RetVal == HAL_OK)												// 如果接收成功
	{
		// 检查是否是SDO请求
		if (RxMeg.StdId == SDO_RX_COB_ID) {
			// 处理SDO请求
			uint8_t cmd = CAN_Rx_Data[0];
			uint16_t index = (CAN_Rx_Data[2] << 8) | CAN_Rx_Data[1];
			uint8_t subindex = CAN_Rx_Data[3];

			if ((cmd & 0xE0) == SDO_CMD_DOWNLOAD) {
				// SDO写请求
				process_sdo_download(index, subindex, &CAN_Rx_Data[4], RxMeg.DLC - 4);
			} else if ((cmd & 0xE0) == SDO_CMD_UPLOAD) {
				// SDO读请求
				uint8_t response_data[4];
				uint8_t response_len;
				process_sdo_upload(index, subindex, response_data, &response_len);
				send_sdo_response(SDO_TX_COB_ID, SDO_RSP_UPLOAD, index, subindex, response_data, response_len);
			}
			return; // SDO处理完成，直接返回
		}

		// 检查是否是命令帧
		if (RxMeg.StdId == CMD_CAN_ID) {
			if (RxMeg.DLC >= 1 && CAN_Rx_Data[0] == CMD_JUMP_TO_APP) {
				printf("Received jump to APP command!\r\n");
				jump_to_app_cmd = 1;
				return; // 处理完命令后直接返回
			}
			else if (RxMeg.DLC >= 2 && CAN_Rx_Data[0] == CMD_ERASE_FLASH) {
				uint8_t partition = CAN_Rx_Data[1];
				if (partition == ERASE_PARTITION_1) {
					printf("Received erase APP1 partition command!\r\n");
					set_target_partition(PARTITION_1);
				} else if (partition == ERASE_PARTITION_2) {
					printf("Received erase APP2 partition command!\r\n");
					set_target_partition(PARTITION_2);
				} else {
					printf("Invalid partition in erase command: %d\r\n", partition);
					return;
				}
				erase_flash_cmd = 1;
				return; // 处理完命令后直接返回
			}

		}

		// 检查是否是固件数据帧，根据CAN ID确定目标分区
		uint32_t target_partition = 0;
		if (RxMeg.StdId == FIRMWARE_APP1_CAN_ID) {
			target_partition = PARTITION_1;
		} else if (RxMeg.StdId == FIRMWARE_APP2_CAN_ID) {
			target_partition = PARTITION_2;
		} else {
			// 不是固件数据帧，忽略
			return;
		}

		// 更新最后接收时间戳
		last_receive_time = HAL_GetTick();

		// 如果是第一包数据，初始化目标分区和Flash地址
		if (total_received_bytes == 0) {
			set_target_partition(target_partition);
			partition_base_addr = get_partition_base_addr(target_partition);
			current_flash_addr = partition_base_addr;
			printf("Starting firmware reception to partition %lu (0x%08lX)...\r\n",
			       target_partition, current_flash_addr);
		}
		
		// printf("CAN RX: ID=0x%03X, DLC=%d, Buffer=%s, Count=%d\r\n", 
		//        RxMeg.StdId, RxMeg.DLC, 
		//        (current_rx_buffer == CAN_RX_BUF_A) ? "A" : "B", 
		//        CAN1_RX_CNT);
		
		// 将数据写入当前接收缓冲区
		for(uint8_t i = 0; i < RxMeg.DLC; i++)
		{
			if(CAN1_RX_CNT < CAN_REC_LEN) {
				current_rx_buffer[CAN1_RX_CNT] = CAN_Rx_Data[i];
				// printf("Received byte %d: 0x%02X\r\n", CAN1_RX_CNT, CAN_Rx_Data[i]);
				CAN1_RX_CNT++;
				total_received_bytes++;
				
				// 缓冲区满了，需要切换缓冲区并标记写Flash
				if(CAN1_RX_CNT >= CAN_REC_LEN) {
					flash_write_pending = 1;
					CAN1_RX_CNT = 0;
					
					// 切换缓冲区
					if(current_rx_buffer == CAN_RX_BUF_A) {
						current_rx_buffer = CAN_RX_BUF_B;
						current_flash_buffer = CAN_RX_BUF_A;
					} else {
						current_rx_buffer = CAN_RX_BUF_A;
						current_flash_buffer = CAN_RX_BUF_B;
					}
					
				}
			}
		}
	}
}

// 流式固件更新初始化
void firmware_stream_init(void)
{
	current_rx_buffer = CAN_RX_BUF_A;
	current_flash_buffer = CAN_RX_BUF_B;
	current_flash_addr = 0;
	partition_base_addr = 0;
	total_received_bytes = 0;
	flash_write_pending = 0;
	last_receive_time = 0;
	CAN1_RX_CNT = 0;
	jump_to_app_cmd = 0;
	erase_flash_cmd = 0;
}

// 检查接收超时（2秒没有新数据认为接收完成）
uint8_t check_receive_timeout(void)
{
	// 只有接收了足够的数据（至少64字节）才认为是固件传输
	if (total_received_bytes >= 64 && last_receive_time > 0) {
		return (HAL_GetTick() - last_receive_time) > 2000;
	}
	
	// 如果只收到少量数据，可能是其他CAN消息，10秒后清除
	if (total_received_bytes > 0 && total_received_bytes < 64 && last_receive_time > 0) {
		if ((HAL_GetTick() - last_receive_time) > 10000) {
			// 清除少量无关数据
			firmware_stream_init();
		}
	}
	return 0;
}

// 重置固件接收状态
void reset_firmware_state(void)
{
	firmware_stream_init();
}

// SDO处理函数实现
static void process_sdo_download(uint16_t index, uint8_t subindex, uint8_t *data, uint8_t len)
{
	switch(index) {
		case OD_BOOT_COMMAND:
			if (subindex == 0 && len >= 1) {
				execute_boot_command(data[0]);
				send_sdo_response(SDO_TX_COB_ID, SDO_RSP_DOWNLOAD, index, subindex, NULL, 0);
			} else {
				send_sdo_abort(SDO_TX_COB_ID, index, subindex, 0x06090030); // 参数值范围错误
			}
			break;

		default:
			send_sdo_abort(SDO_TX_COB_ID, index, subindex, 0x06020000); // 对象不存在
			break;
	}
}

static void process_sdo_upload(uint16_t index, uint8_t subindex, uint8_t *response_data, uint8_t *response_len)
{
	switch(index) {
		case OD_BOOT_STATUS:
			if (subindex == 0) {
				*(uint32_t*)response_data = sdo_boot_status;
				*response_len = 4;
			} else {
				send_sdo_abort(SDO_TX_COB_ID, index, subindex, 0x06090011); // 子索引不存在
			}
			break;

		default:
			send_sdo_abort(SDO_TX_COB_ID, index, subindex, 0x06020000); // 对象不存在
			break;
	}
}

static void send_sdo_response(uint32_t cob_id, uint8_t cmd, uint16_t index, uint8_t subindex, uint8_t *data, uint8_t len)
{
	uint8_t tx_data[8];

	tx_data[0] = cmd;
	tx_data[1] = index & 0xFF;
	tx_data[2] = (index >> 8) & 0xFF;
	tx_data[3] = subindex;

	if (data && len > 0) {
		memcpy(&tx_data[4], data, len > 4 ? 4 : len);
	}

	TxMeg.StdId = cob_id;
	TxMeg.DLC = 8;
	HAL_CAN_AddTxMessage(&hcan1, &TxMeg, tx_data, NULL);
}

static void send_sdo_abort(uint32_t cob_id, uint16_t index, uint8_t subindex, uint32_t abort_code)
{
	uint8_t tx_data[8];

	tx_data[0] = 0x80; // SDO中止命令
	tx_data[1] = index & 0xFF;
	tx_data[2] = (index >> 8) & 0xFF;
	tx_data[3] = subindex;
	tx_data[4] = abort_code & 0xFF;
	tx_data[5] = (abort_code >> 8) & 0xFF;
	tx_data[6] = (abort_code >> 16) & 0xFF;
	tx_data[7] = (abort_code >> 24) & 0xFF;

	TxMeg.StdId = cob_id;
	TxMeg.DLC = 8;
	HAL_CAN_AddTxMessage(&hcan1, &TxMeg, tx_data, NULL);
}

static void execute_boot_command(uint8_t command)
{
	sdo_boot_status = 0; // 重置状态

	switch(command) {
		case CMD_ERASE_APP1:
			printf("SDO: Erasing APP1 partition...\r\n");
			set_target_partition(PARTITION_1);
			erase_flash_cmd = 1;
			break;

		case CMD_ERASE_APP2:
			printf("SDO: Erasing APP2 partition...\r\n");
			set_target_partition(PARTITION_2);
			erase_flash_cmd = 1;
			break;

		case CMD_JUMP_APP1:
			printf("SDO: Jumping to APP1...\r\n");
			set_target_partition(PARTITION_1);
			jump_to_app_cmd = 1;
			break;

		case CMD_JUMP_APP2:
			printf("SDO: Jumping to APP2...\r\n");
			set_target_partition(PARTITION_2);
			jump_to_app_cmd = 1;
			break;

		case CMD_SOFT_RESET:
			printf("SDO: Performing soft reset...\r\n");
			HAL_Delay(100); // 给响应时间
			HAL_NVIC_SystemReset();
			break;

		default:
			printf("SDO: Invalid command: 0x%02X\r\n", command);
			sdo_boot_status = 0x02; // 无效命令错误
			break;
	}
}
