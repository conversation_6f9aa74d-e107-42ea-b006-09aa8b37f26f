#ifndef __IAP_H__
#define __IAP_H__
#include "sys.h"  
#include "app_crc_verify.h"  // 添加CRC验证支持

typedef  void (*iapfun)(void);				//定义一个函数类型的参数

/* 双分区Flash地址定义 */
#define APP1_BASE_ADDR          0x08005000  // APP1分区起始地址
#define APP1_SIZE               0x0000D800  // APP1分区大小 (54KB)
#define APP2_BASE_ADDR          0x08012800  // APP2分区起始地址
#define APP2_SIZE               0x0000D800  // APP2分区大小 (54KB)

/* 兼容性定义 - 保持与现有代码兼容 */
#define FLASH_APP1_ADDR         APP1_BASE_ADDR  // 兼容现有代码

/* RAM启动标志位定义 */
#define BOOT_FLAG_RAM_ADDR        0x20004FF0  // 与APP保持一致的RAM地址
#define BOOT_TO_BOOTLOADER_MAGIC  0x20004F00  // 进入Bootloader的魔术字节
#define BOOT_TO_APP2_MAGIC        0x20004F20  // 进入APP2的魔术字节

/* 启动标志位结构 */
typedef struct {
    uint32_t boot_flag;  // 标志位：0xB00710AD = 进入Bootloader，其他 = 进入APP
} boot_control_t;

/* 双分区管理 */
#define PARTITION_1             1
#define PARTITION_2             2

/* 当前目标分区存储 - 用于固件接收 */
#define TARGET_PARTITION_ADDR   0x20004FEC  // RAM中存储目标分区

void iap_load_app(uint32_t appxaddr);			//执行flash里面的app程序
void iap_write_appbin(uint32_t appxaddr,uint8_t *appbuf,uint32_t applen);	//在指定地址开始,写入bin
uint8_t iap_write_block(uint32_t flash_addr, uint8_t *data, uint32_t len);     //写入单个数据块到Flash
void iap_erase_app_area(void);               //擦除APP区域Flash(108KB)

/* 双分区支持函数 */
void set_target_partition(uint32_t partition);  //设置目标分区（用于固件接收）
uint32_t get_target_partition(void);            //获取目标分区
uint32_t get_partition_base_addr(uint32_t partition); //获取分区基地址
void iap_erase_partition(uint32_t partition);    //擦除指定分区
bool verify_partition_basic(uint32_t partition); //基础分区验证

/* RAM启动标志位管理函数 */
void set_boot_to_bootloader_flag(void);     //设置进入Bootloader标志位
void set_boot_to_app2_flag(void);           //设置进入APP2标志位
void clear_boot_flag(void);                 //清除启动标志位
bool is_boot_to_bootloader_flag_set(void);  //检查是否设置了Bootloader启动标志
bool is_boot_to_app2_flag_set(void);        //检查是否设置了APP2启动标志
void software_reset_to_app(void);           //软件复位并进入APP模式
void software_reset_to_app2(void);          //软件复位并进入APP2模式
void software_reset_to_bootloader(void);    //软件复位并进入Bootloader模式
#endif







































