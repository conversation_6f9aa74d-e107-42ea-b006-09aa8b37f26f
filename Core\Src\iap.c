#include "sys.h"
#include "usart.h"
#include "stmflash.h"
#include "iap.h"
#include "app_crc_verify.h"  // 添加CRC验证支持
#include "stdio.h"
#include <string.h>

iapfun jumpapp;
uint16_t iapbuf[512];
//appxaddr: 应用程序地址
//appbuf: 应用程序CODE数据
//appsize: 应用程序大小(字节)
void iap_write_appbin ( uint32_t appxaddr, uint8_t* appbuf, uint32_t appsize )
{
    uint16_t t;
    uint16_t i = 0;
    uint16_t temp;
    uint32_t fwaddr = appxaddr; //当前写入的地址
    uint8_t* dfu = appbuf;
    for ( t = 0; t < appsize; t += 2 )
    {
        temp = ( uint16_t ) dfu[1] << 8;
        temp += ( uint16_t ) dfu[0];
        dfu += 2; //偏移2个字节
        iapbuf[i++] = temp;
        if ( i == 512 )
        {
            i = 0;
            STMFLASH_Write ( fwaddr, iapbuf, 512 );
            fwaddr += 1024;	//偏移1024字节
        }
    }
    if ( i ) STMFLASH_Write ( fwaddr, iapbuf, i ); //将剩余的一些数据字节写进去
}

//跳转到应用程序
//appxaddr:用户代码起始地址
void iap_load_app ( uint32_t appxaddr )
{
    if ( ( ( * ( volatile uint32_t* ) appxaddr ) & 0x2FFE0000 ) == 0x20000000 )	//检查栈顶地址是否合法
    {
        __set_MSP ( * ( volatile uint32_t* ) appxaddr );					//初始化APP堆栈指针(用户代码区的第一个字为存放堆栈地址)
        jumpapp = ( iapfun ) * ( volatile uint32_t* ) ( appxaddr + 4 );		//用户代码区第二个字为程序开始地址(复位地址)
        MSR_MSP(*(volatile uint32_t*)appxaddr);
        jumpapp();									//跳转到APP
    }
}

// 写入单个数据块到Flash
uint8_t iap_write_block(uint32_t flash_addr, uint8_t *data, uint32_t len)
{
    HAL_StatusTypeDef status = HAL_OK;
    
    // printf("Debug: Writing %d bytes to 0x%08X\r\n", len, flash_addr);
    
    // 解锁Flash
    HAL_FLASH_Unlock();

    // 按字节写入
    for (uint32_t i = 0; i < len; i += 2)
    {
        uint16_t data_to_write;
        
        if (i + 1 < len) {
            // 组合成16位数据
            data_to_write = (uint16_t)data[i + 1] << 8 | (uint16_t)data[i];
        } else {
            // 最后一个字节，高位补0
            data_to_write = (uint16_t)data[i];
        }

        // 写入Flash
        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_HALFWORD, 
                                 flash_addr + i, 
                                 data_to_write);
        
        if (status != HAL_OK) {
            printf("Flash write error at address 0x%08X, status: %d\r\n", 
                   flash_addr + i, status);
            HAL_FLASH_Lock();
            return 1; // 写入失败
        }
    }

    // 锁定Flash
    HAL_FLASH_Lock();
    
    // printf("Debug: Successfully wrote %d bytes\r\n", len);
    return 0; // 写入成功
}

// 擦除APP区域Flash(108KB)
void iap_erase_app_area(void)
{
    FLASH_EraseInitTypeDef EraseInitStruct;
    uint32_t PAGEError = 0;
    HAL_StatusTypeDef status;
    
    printf("Starting Flash erase...\r\n");
    
    // 解锁Flash
    HAL_FLASH_Unlock();

    // 设置擦除参数
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
    EraseInitStruct.PageAddress = FLASH_APP1_ADDR;  // 从0x08005000开始
    EraseInitStruct.NbPages = 108;  // 擦除108个页面(108KB)，每页1KB

    // 执行擦除
    status = HAL_FLASHEx_Erase(&EraseInitStruct, &PAGEError);
    
    if (status == HAL_OK) {
        printf("Flash erase completed successfully! Erased 108KB from 0x%08X\r\n", FLASH_APP1_ADDR);
    } else {
        printf("Flash erase failed! Error code: %d, Page error: 0x%08X\r\n", status, PAGEError);
    }
    
    // 锁定Flash
    HAL_FLASH_Lock();
}

/**
 * @brief 设置进入Bootloader标志位
 */
void set_boot_to_bootloader_flag(void)
{
    boot_control_t *boot_ctrl = (boot_control_t*)BOOT_FLAG_RAM_ADDR;
    boot_ctrl->boot_flag = BOOT_TO_BOOTLOADER_MAGIC;
    printf("Boot flag set: will enter Bootloader on next restart\r\n");
}

/**
 * @brief 设置进入APP2标志位
 */
void set_boot_to_app2_flag(void)
{
    boot_control_t *boot_ctrl = (boot_control_t*)BOOT_FLAG_RAM_ADDR;
    boot_ctrl->boot_flag = BOOT_TO_APP2_MAGIC;
    printf("Boot flag set: will enter APP2 on next restart\r\n");
}

/**
 * @brief 清除启动标志位
 */
void clear_boot_flag(void)
{
    boot_control_t *boot_ctrl = (boot_control_t*)BOOT_FLAG_RAM_ADDR;
    boot_ctrl->boot_flag = 0x00000000;
}

/**
 * @brief 检查是否设置了Bootloader启动标志
 * @return true: 设置了Bootloader启动标志, false: 未设置
 */
bool is_boot_to_bootloader_flag_set(void)
{
    boot_control_t *boot_ctrl = (boot_control_t*)BOOT_FLAG_RAM_ADDR;
    uint32_t flag_value = boot_ctrl->boot_flag;

    printf("Checking boot flag at address 0x%08X\r\n", BOOT_FLAG_RAM_ADDR);
    printf("Boot flag value: 0x%08X, Expected: 0x%08X\r\n", flag_value, BOOT_TO_BOOTLOADER_MAGIC);

    return (flag_value == BOOT_TO_BOOTLOADER_MAGIC);
}

/**
 * @brief 检查是否设置了APP2启动标志
 * @return true: 设置了APP2启动标志, false: 未设置
 */
bool is_boot_to_app2_flag_set(void)
{
    boot_control_t *boot_ctrl = (boot_control_t*)BOOT_FLAG_RAM_ADDR;
    uint32_t flag_value = boot_ctrl->boot_flag;

    printf("Checking APP2 boot flag: 0x%08X, Expected: 0x%08X\r\n", flag_value, BOOT_TO_APP2_MAGIC);

    return (flag_value == BOOT_TO_APP2_MAGIC);
}

/**
 * @brief 软件复位并进入APP模式
 */
void software_reset_to_app(void)
{
    clear_boot_flag();  // 清除标志位，默认进入APP
    printf("Restarting to application...\r\n");
    HAL_Delay(100);  // 确保printf输出完成
    NVIC_SystemReset();
}

/**
 * @brief 软件复位并进入APP2模式
 */
void software_reset_to_app2(void)
{
    set_boot_to_app2_flag();  // 设置APP2标志位
    printf("Restarting to APP2...\r\n");
    HAL_Delay(100);  // 确保printf输出完成
    NVIC_SystemReset();
}

/**
 * @brief 软件复位并进入Bootloader模式
 */
void software_reset_to_bootloader(void)
{
    set_boot_to_bootloader_flag();  // 设置Bootloader标志位
    printf("Restarting to bootloader...\r\n");
    HAL_Delay(100);  // 确保printf输出完成
    NVIC_SystemReset();
}

/* ==================== 双分区支持函数 ==================== */

/**
 * @brief 设置目标分区（用于固件接收）
 */
void set_target_partition(uint32_t partition) {
    if (partition != PARTITION_1 && partition != PARTITION_2) {
        return;
    }

    uint32_t *target_ptr = (uint32_t*)TARGET_PARTITION_ADDR;
    *target_ptr = partition;
    printf("Target partition set to %lu\r\n", partition);
}

/**
 * @brief 获取目标分区
 */
uint32_t get_target_partition(void) {
    uint32_t *target_ptr = (uint32_t*)TARGET_PARTITION_ADDR;
    uint32_t partition = *target_ptr;

    if (partition == PARTITION_1 || partition == PARTITION_2) {
        return partition;
    }

    // 默认返回分区1
    return PARTITION_1;
}

/**
 * @brief 获取分区基地址
 */
uint32_t get_partition_base_addr(uint32_t partition) {
    switch (partition) {
        case PARTITION_1: return APP1_BASE_ADDR;
        case PARTITION_2: return APP2_BASE_ADDR;
        default: return 0;
    }
}

/**
 * @brief 擦除指定分区
 */
void iap_erase_partition(uint32_t partition) {
    uint32_t base_addr = get_partition_base_addr(partition);
    uint32_t size = (partition == PARTITION_1) ? APP1_SIZE : APP2_SIZE;

    if (base_addr == 0) {
        printf("Invalid partition %lu for erase\r\n", partition);
        return;
    }

    printf("Erasing partition %lu (0x%08lX, %lu KB)\r\n",
           partition, base_addr, size / 1024);

    FLASH_EraseInitTypeDef EraseInitStruct;
    uint32_t PAGEError = 0;
    HAL_StatusTypeDef status;

    HAL_FLASH_Unlock();

    // 设置擦除参数
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
    EraseInitStruct.PageAddress = base_addr;
    EraseInitStruct.NbPages = size / 1024;  // 每页1KB

    // 执行擦除
    status = HAL_FLASHEx_Erase(&EraseInitStruct, &PAGEError);

    if (status == HAL_OK) {
        printf("Partition %lu erase completed successfully!\r\n", partition);
    } else {
        printf("Partition %lu erase failed! Error: %d\r\n", partition, status);
    }

    HAL_FLASH_Lock();
}

/**
 * @brief 基础分区验证
 */
bool verify_partition_basic(uint32_t partition) {
    uint32_t base_addr = get_partition_base_addr(partition);
    if (base_addr == 0) {
        return false;
    }

    // 检查向量表
    uint32_t stack_ptr = *(volatile uint32_t*)base_addr;
    if ((stack_ptr & 0x2FFE0000) != 0x20000000) {
        return false;
    }

    uint32_t reset_vector = *(volatile uint32_t*)(base_addr + 4);
    if ((reset_vector & 0xFF000000) != 0x08000000) {
        return false;
    }

    return true;
}
